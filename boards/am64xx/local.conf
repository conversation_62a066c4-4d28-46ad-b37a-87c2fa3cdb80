# CONF_VERSION is increased each time build/conf/ changes incompatibly
CONF_VERSION = "2"

#
# Where to place downloads
#
# During a first build the system will download many different source code
# tarballs from various upstream projects. This can take a while, particularly
# if your network connection is slow. These are all stored in DL_DIR. When
# wiping and rebuilding you can preserve this directory to speed up this part
# of subsequent builds. This directory is safe to share between multiple builds
# on the same machine too.
DL_DIR = "/home/<USER>/yocto_sstate_cache/tisdk_core_sato/downloads"

#
# Where to place shared-state files
#
# BitBake has the capability to accelerate builds based on previously built
# output. This is done using "shared state" files which can be thought of as
# cache objects and this option determines where those files are placed.
#
# You can wipe out TMPDIR leaving this directory intact and the build would
# regenerate from these files if no changes were made to the configuration.
# If changes were made to the configuration, only shared state files where the
# state was still valid would be used (done using checksums).
SSTATE_DIR = "/home/<USER>/yocto_sstate_cache/tisdk_core_sato/sstate-cache"


#
# Shared-state files from other locations
#
# As mentioned above, shared state files are prebuilt cache data objects which
# can be used to accelerate build time. This variable can be used to configure
# the system to search other mirror locations for these objects before it
# builds the data itself.
#
# This can be a filesystem directory, or a remote url such as http or ftp.
# These would contain the sstate-cache results from previous builds (possibly
# from other machines). This variable works like fetcher MIRRORS/PREMIRRORS
# and points to the cache locations to check for the shared objects.
#SSTATE_MIRRORS ?= "\
#file://.* http://someserver.tld/share/sstate/ \n \
#file://.* file:///some/local/dir/sstate/"

#
# Where to place the build output
#
# This option specifies where the bulk of the building work should be done and
# where BitBake should place its temporary files and output. Keep in mind that
# this includes the extraction and compilation of many applications and the toolchain
# which can use Gigabytes of hard disk space.
TMPDIR = "${TOPDIR}/arago-tmp"

# By default, DEPLOY_DIR is inside TMPDIR, but can be changed here to be outside
#DEPLOY_DIR = "${TOPDIR}/deploy"

#
# Machine Selection
#
# You need to select a specific machine to target the build with. There are a selection
# of emulated machines available which can boot and run in the QEMU emulator:
#
#MACHINE ?= "arago"
MACHINE ??="am64xx-evm"

#
# Package Management configuration
#
# This variable lists which packaging formats to enable. Multiple package backends 
# can be enabled at once and the first item listed in the variable will be used 
# to generate the root filesystems.
# Options are:
#  - 'package_deb' for debian style deb files
#  - 'package_ipk' for ipk files are used by opkg (a debian style embedded package manager)
#  - 'package_rpm' for rpm style packages
# E.g.: PACKAGE_CLASSES ?= "package_rpm package_deb package_ipk"
# We default to ipk:
PACKAGE_CLASSES ?= "package_deb"

#
# SDK/ADT target architecture
#
# This variable specified the architecture to build SDK/ADT items for and means
# you can build the SDK packages for architectures other than the machine you are 
# running the build on (i.e. building i686 packages on an x86_64 host._
# Supported values are i686 and x86_64
SDKMACHINE ?= "x86_64"

#
# Extra image configuration defaults
#
# The EXTRA_IMAGE_FEATURES variable allows extra packages to be added to the generated 
# images. Some of these options are added to certain image types automatically. The
# variable can contain the following options:
#  "dbg-pkgs"       - add -dbg packages for all installed packages
#                     (adds symbol information for debugging/profiling)
#  "dev-pkgs"       - add -dev packages for all installed packages
#                     (useful if you want to develop against libs in the image)
#  "tools-sdk"      - add development tools (gcc, make, pkgconfig etc.)
#  "tools-debug"    - add debugging tools (gdb, strace)
#  "tools-profile"  - add profiling tools (oprofile, exmap, lttng valgrind (x86 only))
#  "tools-testapps" - add useful testing tools (ts_print, aplay, arecord etc.)
#  "debug-tweaks"   - make an image suitable for development
#                     e.g. ssh root access has a blank password
# There are other application targets that can be used here too, see
# meta/classes/image.bbclass and meta/classes/core-image.bbclass for more details.
# We default to enabling the debugging tweaks.
# MOVED TO meta-festo-midrange/recipes-images/images/pxc-image-base.bbappend
# EXTRA_IMAGE_FEATURES = "debug-tweaks package-management pxc-virtualization"

#
# Additional image features
#
# The following is a list of additional classes to use when building images which
# enable extra features. Some available options which can be included in this variable 
# are:
#   - 'buildstats' collect build statistics
#   - 'image-swab' to perform host system intrusion detection
# NOTE: mklibs also needs to be explicitly enabled for a given image, see local.conf.extended
USER_CLASSES ?= "buildstats"

# By default disable interactive patch resolution (tasks will just fail instead):
PATCHRESOLVE = "noop"

# By default the machine configuration file sets the IMAGE_FSTYPES.  But if you
# would like to add additional file system types you can uncomment the
# following line and add the additional IMAGE_FSTYPES you want created
# as part of the build flow.  Some common types are listed below and you
# can remove the ones you do not want.
# IMAGE_FSTYPES += "jffs2 cramfs ext2.gz ext3.gz squashfs ubi tar.gz tar.bz2 cpio"

#
# Parallelism Options
#
# These two options control how much parallelism BitBake should use. The first 
# option determines how many tasks bitbake should run in parallel:
#
# BB_NUMBER_THREADS ?= "1"
# 
# The second option controls how many processes make should run in parallel when
# running compile tasks:
#
# PARALLEL_MAKE ?= "-j 1"
#
# For a quad-core machine, BB_NUMBER_THREADS = "4", PARALLEL_MAKE = "-j 4" would
# be appropriate for example
#
# NOTE: By default, bitbake will choose the number of processeors on your host
# so you should not need to set this unless you are wanting to lower the number
# allowed.
#

DISTRO   = "arago"

# Set terminal types by default it expects gnome-terminal
# but we chose xterm
TERMCMD = "${XTERM_TERMCMD}"
TERMCMDRUN = "${XTERM_TERMCMDRUN}"

# Don't generate the mirror tarball for SCM repos, the snapshot is enough
BB_GENERATE_MIRROR_TARBALLS = "0"

# Uncomment this to remove work directory after packaging to save disk space
#INHERIT += "rm_work"

# Keep one set of images by default
RM_OLD_IMAGE = "1"

# Enable local PR service for binary feeds
PRSERV_HOST = "localhost:0"

# Enable hash equiv server
BB_SIGNATURE_HANDLER = "OEEquivHash"
BB_HASHSERVE = "auto"

# It is recommended to activate "buildhistory" for testing the PR service
INHERIT += "buildhistory"
BUILDHISTORY_COMMIT = "1"
### Buildinfo #######################################
# Write build information to the target filesystem on /etc/build. To include
# more variables (e.g. RELEASE_LINE, SWVERSION etc), add them to
# IMAGE_BUILDINFO_VARS.
# MOVED TO meta-festo-midrange/conf/layers.conf
# INHERIT += "image-buildinfo"
# IMAGE_BUILDINFO_VARS:append = " DATETIME DISTRO_NAME IMAGE_BASENAME MACHINE TUNE_PKGARCH"
# IMAGE_BUILDINFO_VARS:append = " MACHINE_FEATURES DISTRO_FEATURES COMMON_FEATURES IMAGE_FEATURES"
# IMAGE_BUILDINFO_VARS:append = " TUNE_FEATURES TARGET_FPU"
# IMAGE_BUILDINFO_VARS:append = " FESTO_TESTS"
# MOVED TO meta-festo-midrange/recipes-images/images/pxc-image-base.bbappend
# IMAGE_INSTALL:append = " devmem2 libgpiod libgpiod-tools libgpiod-dev"
