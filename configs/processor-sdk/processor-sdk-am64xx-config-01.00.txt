# This file takes repo entries in the format
# repo name,repo uri,repo branch,repo commit[,layers=layer1:layer2...:layern]
##meta-festo,https://gitlab.festo.company/festo/yocto/ea/controls/meta/meta-festo.git,main,cepe-v0.0.8,layers=recipes-apps:recipes-arp:recipes-connectivity:recipes-containers:recipes-core:recipes-devtools:recipes-festo-tools:recipes-httpd:recipes-images:recipes-support:recipes-tpm
#meta-ti,https://git.yoctoproject.org/meta-ti,scarthgap,HEAD,layers=meta-ti-extras:meta-ti-bsp
poky,https://gitlab.festo.company/festo/yocto/third-party/poky.git,scarthgap,2034fc38eb4e63984d9bd6b260aa1bf95ce562e4,layers=meta:meta-poky:meta-yocto-bsp
meta-openembedded,https://gitlab.festo.company/festo/yocto/third-party/meta-openembedded.git,scarthgap,72018ca1b1a471226917e8246e8bbf9a374ccf97,layers=meta-networking:meta-python:meta-oe:meta-gnome:meta-filesystems:meta-multimedia:meta-webserver
bitbake,https://gitlab.festo.company/festo/yocto/third-party/bitbake.git,2.8,8714a02e13477a9d97858b3642e05f28247454b5
meta-ti,https://git.yoctoproject.org/meta-ti,scarthgap,7394a1b438eaaddd4e74d354e134f9dc48d489e2,layers=meta-ti-extras:meta-ti-bsp
meta-arm,https://gitlab.festo.company/festo/yocto/third-party/meta-arm.git,scarthgap,58268ddccbe2780de28513273e15193ee949987b,layers=meta-arm:meta-arm-toolchain
meta-security,https://gitlab.festo.company/festo/yocto/third-party/meta-security.git,scarthgap,459d837338ca230254baa2994f870bf6eb9d0139,layers=meta-tpm
meta-rauc,https://gitlab.festo.company/festo/yocto/third-party/meta-rauc.git,scarthgap,1e3e6b334defd7fbf95cb43d23975e7b3de4b520,layers=
meta-virtualization,https://gitlab.festo.company/festo/yocto/third-party/meta-virtualization.git,scarthgap,6f3c1d8f90947408a6587be222fec575a1ca5195,layers=
meta-clang,https://gitlab.festo.company/festo/yocto/third-party/meta-clang.git,scarthgap,2b7433611d80f6d0ee1b04156fa91fc73d3c2665,layers=
meta-hardware,https://gitlab.festo.company/festo/yocto/third-party/pxc/meta-hardware.git,cepe-v0.0.30,9226772dc8e51d42aff57cf04dbf923ca7245777,layers=
meta-pxc,https://gitlab.festo.company/festo/yocto/third-party/pxc/meta-pxc.git,cepe-v0.0.30,b6a34522fab0c3a62d8cce71c0a4d51d5535992a,layers=
meta-arp,https://gitlab.festo.company/festo/yocto/third-party/pxc/meta-arp,cepe-v0.0.30,a8ca200fc6b78dbaa23330cc4d11c32de0ea816e,layers=
meta-festo,https://gitlab.festo.company/festo/yocto/ea/controls/meta/meta-festo.git,main,1d8ae240d46efac10f3c1b0e5c916403fdddf3f,layers=
meta-festo-midrange,https://gitlab.festo.company/festo/yocto/ea/controls/p30/meta-festo-midrange.git,main,HEAD,layers=
OECORELAYERCONF=./sample-files/bblayers.conf.sample
OECORELOCALCONF=./../boards/am64xx/local.conf
BITBAKE_INCLUSIVE_VARS=yes
