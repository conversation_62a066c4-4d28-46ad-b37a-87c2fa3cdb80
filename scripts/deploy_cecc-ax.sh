#!/usr/bin/bash

set -e

# ==============================================================================
#
# Description:
# Script to upload CEPE artifacts to artifactory
#
# Note:
# Deploying to artifactory is not intended to be done from a local full run of
# ./scripts/build.sh, since the ACCESS token is not public available
#
# ==============================================================================
YOCTO_DISTRO_VERSION=$(grep FESTO_OS_VERSION tisdk/sources/meta-festo-midrange/conf/layer.conf | sed 's/FESTO_OS_VERSION = //' | sed 's/\"//g' )
if [[ -z "$CI" ]]; then
  if [[ -z "$ARTIFACTORY_ACCESS_TOKEN" ]]; then
    ARTIFACTORY_ACCESS_TOKEN="$1"
    if [[ -z "$ARTIFACTORY_ACCESS_TOKEN" ]]; then
      ARTIFACTORY_ACCESS_TOKEN=$(cat artifactory_token)
    fi
  fi
  if [[ -z "$YOCTO_DISTRO_VERSION" ]]; then
    YOCTO_DISTRO_VERSION=$2
  fi
  if [[ -z "$ARTIFACTORY_ACCESS_TOKEN" ]] || [[ -z "$YOCTO_DISTRO_VERSION" ]]; then
    echo "=================================================================================="
    echo
    echo "deploy to artifactory ($(date))" 2>&1 | tee -a timestamps.log
    echo
    echo "ERROR: usage:  $0  <artifactory_token> <version>"
    echo ""
    echo "Note: if you are running a full local build, you are missing the token"
    echo "artifactory_access_token which is not public available"
    echo
    echo "=================================================================================="
    exit 1
  fi
fi

if [[ -z "$CI_REPOSITORY_URL" ]]; then
  CI_REPOSITORY_URL="$(git remote get-url --push origin)"
fi

# Note: definition to allow run script outside CI
if [[ -z "$CI_COMMIT_BRANCH" ]]; then
  CI_COMMIT_BRANCH="$(git rev-parse --abbrev-ref HEAD)"
fi

# Note: definition to allow run script outside CI
if [[ -z "$CI_COMMIT_SHORT_SHA" ]]; then
  CI_COMMIT_SHORT_SHA="$(git rev-parse --short HEAD)"
fi

if [[ -z "$ARTIFACTORY_ACCESS_TOKEN" ]]; then
  echo "ERROR: ARTIFACTORY_ACCESS_TOKE is empty, cannot upload artifacts"
  exit 1
fi

# NOTE:
# If the image is not build then the build date-time bellow will have the default
# time stamp 11222444101010. Need a better  approach to get build time and date.
# Bitbake -e pxc-image-base | grep ^IMAGE_VERSION_SUFFIX= seems to be new generated
# at any command call
cd tisdk/build/deploy-ti/
MACHINE=$(ls)
if [[ -z "$MACHINE" ]]; then
  echo "ERROR: deploy-ti folder is empty"
  exit 1
fi
if [[ -z "$CI_PIPELINE_CREATED_AT" ]]; then
  CI_PIPELINE_CREATED_AT="2025-04-15T20:03:12Z"
fi
TIMESTAMP=$(echo $CI_PIPELINE_CREATED_AT | sed 's/[^0-9]//g')
if [[ -z "$TIMESTAMP" ]]; then
      echo "ERROR: deploy must be run in CI pipeline"
      exit 1
fi

CFG_ARCH="arm64"
CFG_TARGET="cecc-ax"
CI_CFG_BUILD_TYPE="develop"
if [[ -z "$CI_PIPELINE_ID" ]]; then
  CI_PIPELINE_ID="222222"
fi
ARTIFACTORY_SERVER_ID="adeartifactory1"
ARTIFACTORY_URL="https://adeartifactory1.de.festo.net"
ARTIFACTORY_BUILD_NUMBER="${YOCTO_DISTRO_VERSION}_${TIMESTAMP}_${CI_COMMIT_SHORT_SHA}"
ARTIFACTORY_CEPE_DIR="festo_${CFG_ARCH}_${CFG_TARGET}_${ARTIFACTORY_BUILD_NUMBER}"

if [[ "$CFG_BUILD_SUBTYPE" =~ custom_+[1-9] ]] && [[ -n "$CI" ]]; then
  ARTIFACTORY_PATH="linux_gen2-generic-dev-local/festo_${CFG_ARCH}_${CFG_TARGET}/${GITLAB_USER_LOGIN}/${ARTIFACTORY_CEPE_DIR}/"
else
  ARTIFACTORY_PATH="linux_gen2-generic-dev-local/festo_${CFG_ARCH}_${CFG_TARGET}/${ARTIFACTORY_CEPE_DIR}"
fi

echo "=================================================================================="
echo
echo -e "\t deploy to artifactory ($(date))" 2>&1 | tee -a timestamps.log
echo
echo "=================================================================================="
jf config remove

if [[ "$(jf config show $ARTIFACTORY_SERVER_ID | grep -c false)" -eq "1" ]]; then
  echo "About to add jf config"
  jf config add "$ARTIFACTORY_SERVER_ID" --url "$ARTIFACTORY_URL" --access-token "$ARTIFACTORY_ACCESS_TOKEN"
fi
jf config show

#if [[ -f "README.md" ]]; then
#  jf rt u \
#  --build-name="$CFG_TARGET" \
#  --build-number="$ARTIFACTORY_BUILD_NUMBER" \
#   --target-props="build.timestamp=${TIMESTAMP};build.type=${CI_CFG_BUILD_TYPE};git.url=${CI_REPOSITORY_URL};git.branch=${CI_COMMIT_BRANCH};git.revision=${CI_COMMIT_SHORT_SHA};gitlab.pipeline-id=${CI_PIPELINE_ID}" \
#  "README.md" "${ARTIFACTORY_PATH}/"
#  echo "INFO: artifactory upload of: README.md"
#fi

#if [[ -f "release_notes.yaml" ]]; then
#  jf rt u \
#  --build-name="$CFG_TARGET" \
#  --build-number="$ARTIFACTORY_BUILD_NUMBER" \
#  --target-props="build.timestamp=${TIMESTAMP};build.type=${CI_CFG_BUILD_TYPE};git.url=${CI_REPOSITORY_URL};git.branch=${CI_COMMIT_BRANCH};git.revision=${CI_COMMIT_SHORT_SHA};gitlab.pipeline-id=${CI_PIPELINE_ID}" \
#  "release_notes.yaml" "${ARTIFACTORY_PATH}/"
#  echo "INFO: artifactory upload of: release_notes.yaml"
#fi

for f in $MACHINE
do
  if [[ $f == images ]]; then
    for i in $(ls images)
    do
      cd images/$i
      find . -type l -delete
      if [[ $i =~ "usb4g" ]]
      then
        tar cf ../../cecc-ax_usb_flasher_image-v${YOCTO_DISTRO_VERSION}-${CI_COMMIT_SHORT_SHA}.tar.gz *.wic.xz
      fi
      if [ -a *.raucb ]
      then
        RB=$(ls *.raucb)
        mv $RB cecc-ax_${YOCTO_DISTRO_VERSION}_updatepackage.raucb
        tar cf ../../cecc-ax_${i}_rauc-v${YOCTO_DISTRO_VERSION}-${CI_COMMIT_SHORT_SHA}.tar.gz *.raucb
      fi
      cd -
    done
  fi
  if [[ $f == sdk ]]; then
    cd sdk
    tar cf ../cecc-ax_sdk-v${YOCTO_DISTRO_VERSION}-${CI_COMMIT_SHORT_SHA}.tar.gz *.sh
    cd -
  fi
done


for file in $(ls *.tar.gz); do
  jf rt u \
  --build-name="$CFG_TARGET" \
  --build-number="$ARTIFACTORY_BUILD_NUMBER" \
  --target-props="build.timestamp=${TIMESTAMP};build.type=${CI_CFG_BUILD_TYPE};git.url=${CI_REPOSITORY_URL};git.branch=${CI_COMMIT_BRANCH};git.revision=${CI_COMMIT_SHORT_SHA};gitlab.pipeline-id=${CI_PIPELINE_ID}" \
  "$file" "${ARTIFACTORY_PATH}/"
  echo ${ARTIFACTORY_PATH}/$file
done

echo "=================================================================================="
echo
echo -e "END OF DEPLOY ($(date))" 2>&1 | tee -a timestamps.log
echo
echo "=================================================================================="

